import request from '@/utils/request'

// 查询借还记录列表
export function listRecord(query) {
  return request({
    url: '/medicine/record/list',
    method: 'get',
    params: query
  })
}

// 查询借还记录详细
export function getRecord(id) {
  return request({
    url: '/medicine/record/' + id,
    method: 'get'
  })
}

// 新增借还记录
export function addRecord(data) {
  return request({
    url: '/medicine/record',
    method: 'post',
    data: data
  })
}

// 修改借还记录
export function updateRecord(data) {
  return request({
    url: '/medicine/record',
    method: 'put',
    data: data
  })
}

// 删除借还记录
export function delRecord(id) {
  return request({
    url: '/medicine/record/' + id,
    method: 'delete'
  })
}
