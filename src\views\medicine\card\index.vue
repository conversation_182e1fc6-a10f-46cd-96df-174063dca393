<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="卡编号" prop="cardCode">
        <el-input
          v-model="queryParams.cardCode"
          placeholder="请输入卡内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="connId">
        <el-input
          v-model="queryParams.connId"
          placeholder="请输入绑定的用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cardList">
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="卡编号" align="center" prop="cardCode" />
      <el-table-column label="用户ID" align="center" prop="connId" />
      <el-table-column label="卡类型" align="center" prop="cardType" >
        <template slot-scope="scope">
          <el-tag :type="scope.row.cardType === 1 ? 'success' : 'danger'">
            {{ scope.row.cardType === 0 ? '用户卡' : '样品卡' }}
          </el-tag>
        </template>
      </el-table-column>
      <!-- cardType -->
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listCard } from "@/api/medicine/card"

export default {
  name: "Card",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据卡表格数据
      cardList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        cardCode: null,
        connId: null
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 查询数据卡列表 */
    getList() {
      this.loading = true
      listCard(this.queryParams).then(response => {
        this.cardList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    }
  }
}
</script>
