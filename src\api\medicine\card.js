import request from '@/utils/request'

// 查询数据卡列表
export function listCard(query) {
  return request({
    url: '/medicine/card/list',
    method: 'get',
    params: query
  })
}

// 查询数据卡详细
export function getCard(id) {
  return request({
    url: '/medicine/card/' + id,
    method: 'get'
  })
}

// 新增数据卡
export function addCard(data) {
  return request({
    url: '/medicine/card',
    method: 'post',
    data: data
  })
}

// 修改数据卡
export function updateCard(data) {
  return request({
    url: '/medicine/card',
    method: 'put',
    data: data
  })
}

// 删除数据卡
export function delCard(id) {
  return request({
    url: '/medicine/card/' + id,
    method: 'delete'
  })
}
