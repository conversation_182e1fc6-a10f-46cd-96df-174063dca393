<template>
    <div class="app-container">
        <!-- 页面标题和返回按钮 -->
        <el-row class="mb8">
            <el-col :span="24">
                <el-page-header @back="goBack" :content="pageTitle">
                    <template slot="title">
                        <span>用户管理</span>
                    </template>
                </el-page-header>
            </el-col>
        </el-row>

        <!-- 用户信息展示 -->
        <el-row class="mb8" v-if="userInfo.nickName || userInfo.userName">
            <el-col :span="24">
                <el-card shadow="never" style="background-color: #f5f7fa;">
                    <div style="display: flex; align-items: center;">
                        <el-avatar :size="40" icon="el-icon-user-solid" style="margin-right: 12px;"></el-avatar>
                        <div>
                            <div style="font-size: 16px; font-weight: bold; color: #303133;">
                                {{ userInfo.nickName || userInfo.userName }}
                            </div>
                            <div style="font-size: 12px; color: #909399; margin-top: 2px;">
                                <span v-if="userInfo.deptName">{{ userInfo.deptName }} | </span>
                                <span>用户ID: {{ userInfo.userId }}</span>
                                <span v-if="userInfo.cardCode"> | 卡号: {{ userInfo.cardCode }}</span>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    v-hasPermi="['medicine:record:export']">导出</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-back" size="mini" @click="goBack">返回</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="recordList">
            <el-table-column label="记录ID" align="center" prop="id" />
            <!-- <el-table-column label="用户ID" align="center" prop="userId" /> -->
            <el-table-column label="用户名" align="center" prop="userName" />
            <!-- <el-table-column label="用户部门" align="center" prop="userDept" /> -->
            <el-table-column label="用户绑定卡号" align="center" prop="userCard" />
            <!-- <el-table-column label="样品ID" align="center" prop="medicineId" /> -->
            <el-table-column label="样品名称" align="center" prop="medicineName" />
            <el-table-column label="样品绑定卡号" align="center" prop="medicineCard" />
            <el-table-column label="不含含量的名称" align="center" prop="medicineNameNor" />
            <!-- <el-table-column label="类型ID" align="center" prop="categoryId" /> -->
            <el-table-column label="类型名称" align="center" prop="categoryName" />
            <el-table-column label="借还类型" align="center" prop="borrow">
                <template slot-scope="scope">
                    <el-tag v-if="scope.row.borrow !== ''"
                        :type="getBorrowType(scope.row.borrow) === 'borrow' ? 'danger' : 'success'">
                        {{ getBorrowText(scope.row.borrow) }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="逾期天数" align="center" prop="borrowLoss" />
            <!-- <el-table-column label="借出时的id" align="center" prop="borrowId" /> -->
            <el-table-column label="借前的重量" align="center" prop="weightBefore" />
            <el-table-column label="还时的重量" align="center" prop="weightNow" />
            <el-table-column label="归还后重量差" align="center" prop="weightBorrow" />
            <!-- <el-table-column label="借出时，重量与上一次重量不一致，记录值" align="center" prop="weightLoss" /> -->
            <el-table-column label="入库时重量" align="center" prop="weightIn" />
            <!-- <el-table-column label="包装重量，且当低于该重量后自动取消关联" align="center" prop="weightLow" /> -->
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import { listRecord } from "@/api/medicine/record"

export default {
    name: "Record",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 借还记录表格数据
            recordList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            },
            // 用户信息
            userInfo: {
                userId: '',
                userName: '',
                nickName: '',
                deptName: '',
                cardCode: ''
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                userName: null,
                userDept: null,
                userCard: null,
                medicineId: null,
                medicineName: null,
                medicineCard: null,
                medicineNameNor: null,
                categoryId: null,
                categoryName: null,
                borrow: null,
                borrowLoss: null,
                borrowId: null,
                weightBefore: null,
                weightNow: null,
                weightBorrow: null,
                weightLoss: null,
                weightIn: null,
                weightLow: null
            },
        }
    },
    computed: {
        // 页面标题
        pageTitle() {
            if (this.userInfo.nickName) {
                return `${this.userInfo.nickName}的借还记录`
            } else if (this.userInfo.userName) {
                return `${this.userInfo.userName}的借还记录`
            } else {
                return `用户借还记录 (ID: ${this.$route.params.userId})`
            }
        }
    },
    created() {
        this.initUserData()
    },
    watch: {
        // 监听路由变化，支持在同一页面切换不同用户
        '$route'(to, from) {
            if (to.params.userId !== from.params.userId) {
                this.initUserData()
            }
        }
    },
    methods: {
        /** 初始化用户数据 */
        initUserData() {
            // 获取路由参数中的用户ID
            const userId = this.$route.params.userId
            console.log('接收到的用户ID:', userId)

            // 获取query中的用户信息
            const userInfoQuery = this.$route.query.userInfo
            if (userInfoQuery) {
                try {
                    this.userInfo = JSON.parse(userInfoQuery)
                    console.log('接收到的用户信息:', this.userInfo)
                } catch (error) {
                    console.error('解析用户信息失败:', error)
                    // 如果解析失败，使用基本信息
                    this.userInfo = {
                        userId: userId,
                        userName: '',
                        nickName: '',
                        deptName: '',
                        cardCode: ''
                    }
                }
            } else {
                // 如果没有传递用户信息，使用基本信息
                this.userInfo = {
                    userId: userId,
                    userName: '',
                    nickName: '',
                    deptName: '',
                    cardCode: ''
                }
            }

            if (userId) {
                // 设置查询参数中的用户ID
                this.queryParams.userId = userId
                console.log('已设置查询参数:', this.queryParams)

                // 重新加载数据
                this.getList()
            } else {
                this.$message.error('未获取到用户ID参数')
                this.goBack()
            }
        },

        /** 查询借还记录列表 */
        getList() {
            this.loading = true
            listRecord(this.queryParams).then(response => {
                this.recordList = response.rows
                this.total = response.total
                this.loading = false
            }).catch(error => {
                console.error('获取借还记录失败:', error)
                this.$message.error('获取借还记录失败')
                this.loading = false
            })
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('medicine/record/export', {
                ...this.queryParams
            }, `record_${new Date().getTime()}.xlsx`)
        },

        /** 返回用户管理页面 */
        goBack() {
            // 关闭当前页面，返回上一页
            this.$router.go(-1)
        },

        /** 获取借还类型 */
        getBorrowType(borrow) {
            // 统一转换为字符串进行比较
            const borrowStr = String(borrow)
            return borrowStr === '0' ? 'borrow' : 'return'
        },

        /** 获取借还类型文本 */
        getBorrowText(borrow) {
            // 统一转换为字符串进行比较
            const borrowStr = String(borrow)
            if (borrowStr === '0') {
                return '借出'
            } else if (borrowStr === '1') {
                return '归还'
            } else {
                return '未知'
            }
        },
    }
}
</script>
